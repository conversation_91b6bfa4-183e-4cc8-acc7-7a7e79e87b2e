/**
 * 简化版浮层面板模块
 * 确保能正确加载和初始化
 */

console.log('🚀 开始加载浮层面板模块...');

// 浮层状态
let floatingPanel = null;
let isDragging = false;
let dragOffset = { x: 0, y: 0 };
let isExpanded = false;

/**
 * 初始化浮层面板
 */
async function initializeFloatingPanel() {
    try {
        console.log('🚀 初始化浮层面板...');
        
        // 检查是否已存在浮层
        if (floatingPanel) {
            console.log('浮层已存在，跳过初始化');
            return;
        }
        
        // 创建浮层DOM
        createFloatingPanel();
        
        // 设置事件监听
        setupEventListeners();
        
        console.log('✅ 浮层面板初始化完成');
    } catch (error) {
        console.error('❌ 浮层面板初始化失败:', error);
    }
}

/**
 * 创建浮层DOM结构
 */
function createFloatingPanel() {
    // 创建浮层容器
    floatingPanel = document.createElement('div');
    floatingPanel.id = 'automation-floating-panel';
    floatingPanel.className = 'automation-floating-panel collapsed';
    
    // 设置浮层样式
    floatingPanel.innerHTML = `
        <div class="panel-header" id="panel-header">
            <div class="panel-icon">🤖</div>
            <div class="panel-title">自动化助手</div>
            <div class="panel-toggle" id="panel-toggle">▶</div>
        </div>
        <div class="panel-content" id="panel-content">
            <div class="workflow-selector">
                <select id="workflow-select" class="workflow-select">
                    <option value="">选择工作流...</option>
                </select>
                <button id="refresh-workflows" class="btn-icon" title="刷新工作流列表">🔄</button>
            </div>
            
            <div class="execution-controls">
                <button id="start-workflow" class="btn-primary" disabled>▶ 开始</button>
                <button id="pause-workflow" class="btn-secondary" disabled>⏸ 暂停</button>
                <button id="stop-workflow" class="btn-danger" disabled>⏹ 停止</button>
            </div>
            
            <div class="execution-status" id="execution-status">
                <div class="status-text">等待执行...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="step-info" id="step-info">步骤: 0/0</div>
            </div>
            
            <div class="panel-footer">
                <button id="open-designer" class="btn-link">打开设计器</button>
            </div>
        </div>
    `;
    
    // 添加CSS样式
    addFloatingPanelStyles();
    
    // 插入到页面
    document.body.appendChild(floatingPanel);
    
    console.log('✅ 浮层DOM创建完成');
}

/**
 * 添加浮层CSS样式
 */
function addFloatingPanelStyles() {
    const styleId = 'automation-floating-panel-styles';
    
    // 检查是否已添加样式
    if (document.getElementById(styleId)) {
        return;
    }
    
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
        .automation-floating-panel {
            position: fixed;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            width: 280px;
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 999999;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            transition: all 0.3s ease;
            user-select: none;
        }
        
        .automation-floating-panel.collapsed {
            width: 60px;
            height: 60px;
            border-radius: 30px;
        }
        
        .automation-floating-panel.collapsed .panel-content {
            display: none;
        }
        
        .panel-header {
            display: flex;
            align-items: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px 8px 0 0;
            cursor: move;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .automation-floating-panel.collapsed .panel-header {
            justify-content: center;
            padding: 18px;
            border-radius: 30px;
            border-bottom: none;
        }
        
        .panel-icon {
            font-size: 18px;
            margin-right: 8px;
        }
        
        .automation-floating-panel.collapsed .panel-icon {
            margin-right: 0;
        }
        
        .panel-title {
            flex: 1;
            font-weight: 600;
            color: #333;
        }
        
        .automation-floating-panel.collapsed .panel-title {
            display: none;
        }
        
        .panel-toggle {
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .panel-toggle:hover {
            background-color: #e9ecef;
        }
        
        .automation-floating-panel.collapsed .panel-toggle {
            display: none;
        }
        
        .panel-content {
            padding: 16px;
        }
        
        .workflow-selector {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .workflow-select {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn-icon {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fff;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-icon:hover {
            background: #f8f9fa;
        }
        
        .execution-controls {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .btn-primary, .btn-secondary, .btn-danger {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover:not(:disabled) {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover:not(:disabled) {
            background: #545b62;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover:not(:disabled) {
            background: #c82333;
        }
        
        .btn-primary:disabled, .btn-secondary:disabled, .btn-danger:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .execution-status {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 16px;
        }
        
        .status-text {
            font-weight: 500;
            margin-bottom: 8px;
            color: #333;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        
        .progress-fill {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .step-info {
            font-size: 12px;
            color: #666;
        }
        
        .panel-footer {
            border-top: 1px solid #e0e0e0;
            padding-top: 12px;
        }
        
        .btn-link {
            background: none;
            border: none;
            color: #007bff;
            cursor: pointer;
            font-size: 12px;
            text-decoration: underline;
        }
        
        .btn-link:hover {
            color: #0056b3;
        }
    `;
    
    document.head.appendChild(style);
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 拖拽功能
    const header = document.getElementById('panel-header');
    if (header) {
        header.addEventListener('mousedown', startDrag);
    }
    
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', endDrag);
    
    // 展开/折叠
    const toggle = document.getElementById('panel-toggle');
    if (toggle) {
        toggle.addEventListener('click', togglePanel);
    }
    
    // 双击头部也可以展开/折叠
    if (header) {
        header.addEventListener('dblclick', togglePanel);
    }
    
    console.log('✅ 事件监听器设置完成');
}

/**
 * 拖拽相关函数
 */
function startDrag(e) {
    if (e.target.id === 'panel-toggle') return; // 不在切换按钮上拖拽
    
    isDragging = true;
    const rect = floatingPanel.getBoundingClientRect();
    dragOffset.x = e.clientX - rect.left;
    dragOffset.y = e.clientY - rect.top;
    
    floatingPanel.style.transition = 'none';
    e.preventDefault();
}

function drag(e) {
    if (!isDragging) return;
    
    const x = e.clientX - dragOffset.x;
    const y = e.clientY - dragOffset.y;
    
    // 限制在视窗内
    const maxX = window.innerWidth - floatingPanel.offsetWidth;
    const maxY = window.innerHeight - floatingPanel.offsetHeight;
    
    const constrainedX = Math.max(0, Math.min(x, maxX));
    const constrainedY = Math.max(0, Math.min(y, maxY));
    
    floatingPanel.style.left = constrainedX + 'px';
    floatingPanel.style.top = constrainedY + 'px';
    floatingPanel.style.transform = 'none';
}

function endDrag() {
    if (isDragging) {
        isDragging = false;
        floatingPanel.style.transition = 'all 0.3s ease';
    }
}

/**
 * 展开/折叠面板
 */
function togglePanel() {
    isExpanded = !isExpanded;
    const toggle = document.getElementById('panel-toggle');
    
    if (isExpanded) {
        floatingPanel.classList.remove('collapsed');
        if (toggle) toggle.textContent = '◀';
    } else {
        floatingPanel.classList.add('collapsed');
        if (toggle) toggle.textContent = '▶';
    }
}

// 导出到全局作用域供其他模块使用
window.FloatingPanel = {
    initialize: initializeFloatingPanel,
    show: () => {
        if (floatingPanel) {
            floatingPanel.style.display = 'block';
        }
    },
    hide: () => {
        if (floatingPanel) {
            floatingPanel.style.display = 'none';
        }
    },
    toggle: togglePanel,
    isVisible: () => floatingPanel && floatingPanel.style.display !== 'none'
};

console.log('✅ 浮层面板模块已加载');
