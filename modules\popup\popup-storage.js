/**
 * 弹窗存储管理模块
 * 负责Chrome Extension Storage管理、数据持久化和存储监听
 */

import { debugLog, safeJsonParse, safeJsonStringify } from '../../shared/popup/popup-utils.js';
import { STORAGE_KEY } from '../../shared/popup/popup-constants.js';
import {
    WorkflowStorage,
    StateCache,
    onStorageChanged,
    isStorageApiAvailable
} from '../../shared/storage/chrome-storage.js';
import { performMigration } from '../../shared/storage/storage-migration.js';

/**
 * 从Chrome Extension Storage获取工作流列表
 * @returns {Promise<Array>} 工作流列表
 */
export async function getWorkflowsFromStorage() {
    try {
        debugLog('正在读取Chrome Storage，键名:', STORAGE_KEY);
        console.log('🔍 [DEBUG] getWorkflowsFromStorage 开始执行，键名:', STORAGE_KEY);

        // 检查是否需要迁移数据
        if (!isStorageApiAvailable()) {
            console.warn('Chrome Storage API 不可用，降级到localStorage');
            return getWorkflowsFromLocalStorage();
        }

        // 尝试从Chrome Storage获取数据
        const workflows = await WorkflowStorage.getWorkflows();

        // 如果Chrome Storage为空，尝试迁移localStorage数据
        if (!workflows || workflows.length === 0) {
            console.log('🔄 Chrome Storage为空，尝试迁移localStorage数据...');
            const migrationResult = await performMigration();
            if (migrationResult.success) {
                console.log('✅ 数据迁移成功，重新获取工作流');
                return await WorkflowStorage.getWorkflows();
            }
        }

        debugLog(`从Chrome Storage读取到 ${workflows.length} 个工作流`);
        console.log('🔍 [DEBUG] 成功读取工作流数据:', workflows);
        return workflows;
    } catch (error) {
        console.error('读取工作流数据失败:', error);
        // 降级到localStorage
        return getWorkflowsFromLocalStorage();
    }
}

/**
 * 降级方案：从localStorage获取工作流列表
 * @returns {Array} 工作流列表
 */
function getWorkflowsFromLocalStorage() {
    try {
        const data = localStorage.getItem(STORAGE_KEY);
        if (!data) {
            return [];
        }

        const workflows = safeJsonParse(data, []);
        const result = Array.isArray(workflows) ? workflows : [];
        return result;
    } catch (error) {
        console.error('从localStorage读取数据失败:', error);
        return [];
    }
}

/**
 * 保存工作流列表到Chrome Extension Storage
 * @param {Array} workflows - 工作流列表
 * @returns {Promise<boolean>} 保存是否成功
 */
export async function saveWorkflowsToStorage(workflows) {
    try {
        if (!Array.isArray(workflows)) {
            console.error('工作流数据必须是数组');
            return false;
        }

        // 优先使用Chrome Storage
        if (isStorageApiAvailable()) {
            const success = await WorkflowStorage.saveWorkflows(workflows);
            if (success) {
                debugLog(`已保存 ${workflows.length} 个工作流到Chrome Storage`);
                return true;
            }
        }

        // 降级到localStorage
        const jsonData = safeJsonStringify(workflows);
        localStorage.setItem(STORAGE_KEY, jsonData);
        debugLog(`已保存 ${workflows.length} 个工作流到localStorage (降级)`);
        return true;
    } catch (error) {
        console.error('保存工作流数据失败:', error);
        return false;
    }
}

/**
 * 初始化存储监听器
 * 监听Chrome Storage和localStorage的变化
 */
export function initializeStorageListener() {
    debugLog('初始化存储监听器');

    // Chrome Storage监听器
    if (isStorageApiAvailable()) {
        const unsubscribe = onStorageChanged((changes, areaName) => {
            if (changes[STORAGE_KEY]) {
                debugLog('检测到Chrome Storage工作流数据变化');

                // 触发自定义事件，通知其他模块数据已更新
                const event = new CustomEvent('workflowsUpdated', {
                    detail: {
                        source: 'chrome-storage',
                        areaName: areaName,
                        oldValue: changes[STORAGE_KEY].oldValue,
                        newValue: changes[STORAGE_KEY].newValue
                    }
                });
                window.dispatchEvent(event);
            }
        });

        // 保存取消监听的函数，供清理时使用
        window._storageUnsubscribe = unsubscribe;
    }

    // localStorage监听器（降级方案）
    window.addEventListener('storage', (e) => {
        if (e.key === STORAGE_KEY) {
            debugLog('检测到localStorage工作流数据变化');

            // 触发自定义事件，通知其他模块数据已更新
            const event = new CustomEvent('workflowsUpdated', {
                detail: {
                    source: 'localStorage',
                    oldValue: e.oldValue,
                    newValue: e.newValue
                }
            });
            window.dispatchEvent(event);
        }
    });
}

/**
 * 调试localStorage内容
 * 用于开发和调试
 */
export function debugLocalStorage() {
    if (!debugLog.enabled) return;

    debugLog('调试localStorage内容:');
    debugLog('localStorage长度:', localStorage.length);

    // 显示所有键
    const keys = [];
    for (let i = 0; i < localStorage.length; i++) {
        keys.push(localStorage.key(i));
    }
    debugLog('所有键名:', keys);

    // 显示工作流数据
    const workflowData = localStorage.getItem(STORAGE_KEY);
    if (workflowData) {
        try {
            const workflows = JSON.parse(workflowData);
            debugLog(`工作流数据 (${STORAGE_KEY}):`, workflows);
            debugLog('工作流数量:', workflows.length);

            workflows.forEach((workflow, index) => {
                debugLog(`工作流 ${index}:`, {
                    name: workflow.name,
                    steps: workflow.steps?.length || 0,
                    createdAt: workflow.createdAt,
                    updatedAt: workflow.updatedAt
                });
            });
        } catch (error) {
            console.error('解析工作流数据失败:', error);
        }
    } else {
        debugLog('没有找到工作流数据');
    }

    // 显示存储使用情况
    let totalSize = 0;
    for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
            totalSize += localStorage[key].length;
        }
    }
    debugLog(`localStorage总大小: ${(totalSize / 1024).toFixed(2)} KB`);
}

/**
 * 清理过期的缓存数据
 * @param {number} maxAge - 最大保存时间(毫秒)，默认7天
 */
export function cleanupExpiredCache(maxAge = 7 * 24 * 60 * 60 * 1000) {
    const now = Date.now();
    const keysToRemove = [];

    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);

        // 检查缓存键
        if (key && key.includes('_cache')) {
            try {
                const data = safeJsonParse(localStorage.getItem(key));
                if (data && data.timestamp && (now - data.timestamp > maxAge)) {
                    keysToRemove.push(key);
                }
            } catch (error) {
                // 如果解析失败，也标记为删除
                keysToRemove.push(key);
            }
        }
    }

    // 删除过期的缓存
    keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        debugLog(`已删除过期缓存: ${key}`);
    });

    if (keysToRemove.length > 0) {
        debugLog(`清理了 ${keysToRemove.length} 个过期缓存项`);
    }
}

/**
 * 获取存储使用情况统计
 * @returns {Object} 存储统计信息
 */
export function getStorageStats() {
    let totalSize = 0;
    let workflowSize = 0;
    let cacheSize = 0;
    let otherSize = 0;

    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        const value = localStorage.getItem(key);
        const size = value ? value.length : 0;

        totalSize += size;

        if (key === STORAGE_KEY) {
            workflowSize += size;
        } else if (key && key.includes('_cache')) {
            cacheSize += size;
        } else {
            otherSize += size;
        }
    }

    return {
        total: {
            size: totalSize,
            sizeKB: (totalSize / 1024).toFixed(2),
            count: localStorage.length
        },
        workflows: {
            size: workflowSize,
            sizeKB: (workflowSize / 1024).toFixed(2)
        },
        cache: {
            size: cacheSize,
            sizeKB: (cacheSize / 1024).toFixed(2)
        },
        other: {
            size: otherSize,
            sizeKB: (otherSize / 1024).toFixed(2)
        }
    };
}

/**
 * 备份工作流数据
 * @returns {string} 备份的JSON字符串
 */
export function backupWorkflows() {
    const workflows = getWorkflowsFromStorage();
    const backup = {
        version: '1.0',
        timestamp: Date.now(),
        workflows: workflows
    };

    debugLog(`已备份 ${workflows.length} 个工作流`);
    return safeJsonStringify(backup);
}

/**
 * 从备份恢复工作流数据
 * @param {string} backupData - 备份的JSON字符串
 * @returns {boolean} 恢复是否成功
 */
export function restoreWorkflows(backupData) {
    try {
        const backup = safeJsonParse(backupData);

        if (!backup || !backup.workflows || !Array.isArray(backup.workflows)) {
            console.error('备份数据格式无效');
            return false;
        }

        const success = saveWorkflowsToStorage(backup.workflows);
        if (success) {
            debugLog(`已从备份恢复 ${backup.workflows.length} 个工作流`);

            // 触发更新事件
            const event = new CustomEvent('workflowsRestored', {
                detail: { workflows: backup.workflows }
            });
            window.dispatchEvent(event);
        }

        return success;
    } catch (error) {
        console.error('恢复工作流数据失败:', error);
        return false;
    }
}

/**
 * 检查存储配额
 * @returns {Object} 配额信息
 */
export function checkStorageQuota() {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
        return navigator.storage.estimate().then(estimate => {
            return {
                quota: estimate.quota,
                usage: estimate.usage,
                available: estimate.quota - estimate.usage,
                usagePercentage: ((estimate.usage / estimate.quota) * 100).toFixed(2)
            };
        });
    } else {
        // 降级方案：尝试写入测试数据来估算剩余空间
        return Promise.resolve({
            quota: null,
            usage: null,
            available: null,
            usagePercentage: null,
            note: '浏览器不支持存储配额API'
        });
    }
}