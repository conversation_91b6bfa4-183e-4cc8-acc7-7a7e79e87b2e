/**
 * 浮层面板模块
 * 在每个页面植入可拖拽的浮层，提供工作流执行控制功能
 */

// 由于content script环境限制，直接实现存储功能而不使用import
// Chrome Storage API 功能
const ChromeStorage = {
    async getWorkflows() {
        try {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                const result = await chrome.storage.local.get('automationWorkflows');
                return result.automationWorkflows || [];
            } else {
                // 降级到localStorage
                const data = localStorage.getItem('automationWorkflows');
                return data ? JSON.parse(data) : [];
            }
        } catch (error) {
            console.error('获取工作流失败:', error);
            return [];
        }
    },

    onChanged(callback) {
        if (typeof chrome !== 'undefined' && chrome.storage) {
            const listener = (changes, areaName) => {
                callback(changes, areaName);
            };
            chrome.storage.onChanged.addListener(listener);
            return () => chrome.storage.onChanged.removeListener(listener);
        } else {
            // localStorage监听
            const listener = (e) => {
                if (e.key === 'automationWorkflows') {
                    callback({ [e.key]: { oldValue: e.oldValue, newValue: e.newValue } }, 'localStorage');
                }
            };
            window.addEventListener('storage', listener);
            return () => window.removeEventListener('storage', listener);
        }
    }
};

// 浮层状态
let floatingPanel = null;
let isDragging = false;
let dragOffset = { x: 0, y: 0 };
let isExpanded = false;
let currentWorkflows = [];
let selectedWorkflowIndex = -1;
let executionState = {
    isRunning: false,
    isPaused: false,
    currentStep: 0,
    totalSteps: 0
};

/**
 * 初始化浮层面板
 */
export async function initializeFloatingPanel() {
    try {
        console.log('🚀 初始化浮层面板...');
        
        // 检查是否已存在浮层
        if (floatingPanel) {
            console.log('浮层已存在，跳过初始化');
            return;
        }
        
        // 创建浮层DOM
        createFloatingPanel();
        
        // 加载工作流数据
        await loadWorkflows();
        
        // 设置事件监听
        setupEventListeners();
        
        // 监听存储变化
        setupStorageListener();
        
        console.log('✅ 浮层面板初始化完成');
    } catch (error) {
        console.error('❌ 浮层面板初始化失败:', error);
    }
}

/**
 * 创建浮层DOM结构
 */
function createFloatingPanel() {
    // 创建浮层容器
    floatingPanel = document.createElement('div');
    floatingPanel.id = 'automation-floating-panel';
    floatingPanel.className = 'automation-floating-panel collapsed';
    
    // 设置浮层样式
    floatingPanel.innerHTML = `
        <div class="panel-header" id="panel-header">
            <div class="panel-icon">🤖</div>
            <div class="panel-title">自动化助手</div>
            <div class="panel-toggle" id="panel-toggle">▶</div>
        </div>
        <div class="panel-content" id="panel-content">
            <div class="workflow-selector">
                <select id="workflow-select" class="workflow-select">
                    <option value="">选择工作流...</option>
                </select>
                <button id="refresh-workflows" class="btn-icon" title="刷新工作流列表">🔄</button>
            </div>
            
            <div class="execution-controls">
                <button id="start-workflow" class="btn-primary" disabled>▶ 开始</button>
                <button id="pause-workflow" class="btn-secondary" disabled>⏸ 暂停</button>
                <button id="stop-workflow" class="btn-danger" disabled>⏹ 停止</button>
            </div>
            
            <div class="execution-status" id="execution-status">
                <div class="status-text">等待执行...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="step-info" id="step-info">步骤: 0/0</div>
            </div>
            
            <div class="panel-footer">
                <button id="open-designer" class="btn-link">打开设计器</button>
            </div>
        </div>
    `;
    
    // 添加CSS样式
    addFloatingPanelStyles();
    
    // 插入到页面
    document.body.appendChild(floatingPanel);
    
    console.log('✅ 浮层DOM创建完成');
}

/**
 * 添加浮层CSS样式
 */
function addFloatingPanelStyles() {
    const styleId = 'automation-floating-panel-styles';
    
    // 检查是否已添加样式
    if (document.getElementById(styleId)) {
        return;
    }
    
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
        .automation-floating-panel {
            position: fixed;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            width: 280px;
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 999999;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            transition: all 0.3s ease;
            user-select: none;
        }
        
        .automation-floating-panel.collapsed {
            width: 60px;
            height: 60px;
            border-radius: 30px;
        }
        
        .automation-floating-panel.collapsed .panel-content {
            display: none;
        }
        
        .panel-header {
            display: flex;
            align-items: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px 8px 0 0;
            cursor: move;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .automation-floating-panel.collapsed .panel-header {
            justify-content: center;
            padding: 18px;
            border-radius: 30px;
            border-bottom: none;
        }
        
        .panel-icon {
            font-size: 18px;
            margin-right: 8px;
        }
        
        .automation-floating-panel.collapsed .panel-icon {
            margin-right: 0;
        }
        
        .panel-title {
            flex: 1;
            font-weight: 600;
            color: #333;
        }
        
        .automation-floating-panel.collapsed .panel-title {
            display: none;
        }
        
        .panel-toggle {
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .panel-toggle:hover {
            background-color: #e9ecef;
        }
        
        .automation-floating-panel.collapsed .panel-toggle {
            display: none;
        }
        
        .panel-content {
            padding: 16px;
        }
        
        .workflow-selector {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .workflow-select {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn-icon {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fff;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-icon:hover {
            background: #f8f9fa;
        }
        
        .execution-controls {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .btn-primary, .btn-secondary, .btn-danger {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover:not(:disabled) {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover:not(:disabled) {
            background: #545b62;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover:not(:disabled) {
            background: #c82333;
        }
        
        .btn-primary:disabled, .btn-secondary:disabled, .btn-danger:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .execution-status {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 16px;
        }
        
        .status-text {
            font-weight: 500;
            margin-bottom: 8px;
            color: #333;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        
        .progress-fill {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .step-info {
            font-size: 12px;
            color: #666;
        }
        
        .panel-footer {
            border-top: 1px solid #e0e0e0;
            padding-top: 12px;
        }
        
        .btn-link {
            background: none;
            border: none;
            color: #007bff;
            cursor: pointer;
            font-size: 12px;
            text-decoration: underline;
        }
        
        .btn-link:hover {
            color: #0056b3;
        }
    `;
    
    document.head.appendChild(style);
}

/**
 * 加载工作流数据
 */
async function loadWorkflows() {
    try {
        console.log('📦 加载工作流数据...');

        currentWorkflows = await ChromeStorage.getWorkflows();

        updateWorkflowSelector();
        console.log(`✅ 加载了 ${currentWorkflows.length} 个工作流`);
    } catch (error) {
        console.error('❌ 加载工作流失败:', error);
        currentWorkflows = [];
        updateWorkflowSelector();
    }
}

/**
 * 更新工作流选择器
 */
function updateWorkflowSelector() {
    const select = document.getElementById('workflow-select');
    if (!select) return;

    // 清空现有选项
    select.innerHTML = '<option value="">选择工作流...</option>';

    // 添加工作流选项
    currentWorkflows.forEach((workflow, index) => {
        const option = document.createElement('option');
        option.value = index;
        option.textContent = workflow.name || `工作流 ${index + 1}`;
        select.appendChild(option);
    });

    // 恢复之前的选择
    if (selectedWorkflowIndex >= 0 && selectedWorkflowIndex < currentWorkflows.length) {
        select.value = selectedWorkflowIndex;
        updateExecutionControls();
    }
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 拖拽功能
    const header = document.getElementById('panel-header');
    header.addEventListener('mousedown', startDrag);
    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', endDrag);

    // 展开/折叠
    const toggle = document.getElementById('panel-toggle');
    toggle.addEventListener('click', togglePanel);

    // 双击头部也可以展开/折叠
    header.addEventListener('dblclick', togglePanel);

    // 工作流选择
    const workflowSelect = document.getElementById('workflow-select');
    workflowSelect.addEventListener('change', onWorkflowSelect);

    // 刷新工作流
    const refreshBtn = document.getElementById('refresh-workflows');
    refreshBtn.addEventListener('click', refreshWorkflows);

    // 执行控制按钮
    const startBtn = document.getElementById('start-workflow');
    const pauseBtn = document.getElementById('pause-workflow');
    const stopBtn = document.getElementById('stop-workflow');

    startBtn.addEventListener('click', startWorkflow);
    pauseBtn.addEventListener('click', pauseWorkflow);
    stopBtn.addEventListener('click', stopWorkflow);

    // 打开设计器
    const designerBtn = document.getElementById('open-designer');
    designerBtn.addEventListener('click', openDesigner);

    // 监听来自background的消息
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.onMessage.addListener(handleBackgroundMessage);
    }
}

/**
 * 设置存储监听器
 */
function setupStorageListener() {
    ChromeStorage.onChanged((changes, areaName) => {
        if (changes['automationWorkflows']) {
            console.log('🔄 检测到工作流数据变化，重新加载...');
            loadWorkflows();
        }
    });
}

/**
 * 拖拽相关函数
 */
function startDrag(e) {
    if (e.target.id === 'panel-toggle') return; // 不在切换按钮上拖拽

    isDragging = true;
    const rect = floatingPanel.getBoundingClientRect();
    dragOffset.x = e.clientX - rect.left;
    dragOffset.y = e.clientY - rect.top;

    floatingPanel.style.transition = 'none';
    e.preventDefault();
}

function drag(e) {
    if (!isDragging) return;

    const x = e.clientX - dragOffset.x;
    const y = e.clientY - dragOffset.y;

    // 限制在视窗内
    const maxX = window.innerWidth - floatingPanel.offsetWidth;
    const maxY = window.innerHeight - floatingPanel.offsetHeight;

    const constrainedX = Math.max(0, Math.min(x, maxX));
    const constrainedY = Math.max(0, Math.min(y, maxY));

    floatingPanel.style.left = constrainedX + 'px';
    floatingPanel.style.top = constrainedY + 'px';
    floatingPanel.style.transform = 'none';
}

function endDrag() {
    if (isDragging) {
        isDragging = false;
        floatingPanel.style.transition = 'all 0.3s ease';
    }
}

/**
 * 展开/折叠面板
 */
function togglePanel() {
    isExpanded = !isExpanded;
    const toggle = document.getElementById('panel-toggle');

    if (isExpanded) {
        floatingPanel.classList.remove('collapsed');
        toggle.textContent = '◀';
    } else {
        floatingPanel.classList.add('collapsed');
        toggle.textContent = '▶';
    }
}

/**
 * 工作流选择处理
 */
function onWorkflowSelect(e) {
    selectedWorkflowIndex = parseInt(e.target.value);
    updateExecutionControls();
}

/**
 * 刷新工作流列表
 */
async function refreshWorkflows() {
    const refreshBtn = document.getElementById('refresh-workflows');
    refreshBtn.textContent = '⏳';
    refreshBtn.disabled = true;

    try {
        await loadWorkflows();
    } finally {
        refreshBtn.textContent = '🔄';
        refreshBtn.disabled = false;
    }
}

/**
 * 更新执行控制按钮状态
 */
function updateExecutionControls() {
    const startBtn = document.getElementById('start-workflow');
    const pauseBtn = document.getElementById('pause-workflow');
    const stopBtn = document.getElementById('stop-workflow');

    const hasWorkflow = selectedWorkflowIndex >= 0 && currentWorkflows[selectedWorkflowIndex];

    if (executionState.isRunning) {
        startBtn.disabled = true;
        pauseBtn.disabled = false;
        stopBtn.disabled = false;

        if (executionState.isPaused) {
            startBtn.textContent = '▶ 继续';
            startBtn.disabled = false;
            pauseBtn.textContent = '⏸ 已暂停';
            pauseBtn.disabled = true;
        } else {
            startBtn.textContent = '▶ 开始';
            pauseBtn.textContent = '⏸ 暂停';
        }
    } else {
        startBtn.disabled = !hasWorkflow;
        pauseBtn.disabled = true;
        stopBtn.disabled = true;
        startBtn.textContent = '▶ 开始';
        pauseBtn.textContent = '⏸ 暂停';
    }
}

/**
 * 开始/继续工作流
 */
async function startWorkflow() {
    if (selectedWorkflowIndex < 0 || !currentWorkflows[selectedWorkflowIndex]) {
        alert('请先选择一个工作流');
        return;
    }

    const workflow = currentWorkflows[selectedWorkflowIndex];

    try {
        if (executionState.isPaused) {
            // 继续执行
            console.log('🔄 继续执行工作流...');
            await sendMessageToBackground({
                action: 'resumeExecution'
            });
        } else {
            // 开始新的执行
            console.log('🚀 开始执行工作流:', workflow.name);
            executionState.isRunning = true;
            executionState.isPaused = false;
            executionState.currentStep = 0;
            executionState.totalSteps = workflow.steps ? workflow.steps.length : 0;

            updateExecutionControls();
            updateExecutionStatus('正在启动...', 0);

            await sendMessageToBackground({
                action: 'executeSteps',
                steps: workflow.steps || []
            });
        }
    } catch (error) {
        console.error('❌ 启动工作流失败:', error);
        alert('启动工作流失败: ' + error.message);
        resetExecutionState();
    }
}

/**
 * 暂停工作流
 */
async function pauseWorkflow() {
    try {
        console.log('⏸ 暂停工作流执行...');
        executionState.isPaused = true;
        updateExecutionControls();
        updateExecutionStatus('已暂停', null);

        await sendMessageToBackground({
            action: 'pauseExecution'
        });
    } catch (error) {
        console.error('❌ 暂停工作流失败:', error);
    }
}

/**
 * 停止工作流
 */
async function stopWorkflow() {
    try {
        console.log('⏹ 停止工作流执行...');

        await sendMessageToBackground({
            action: 'stopExecution'
        });

        resetExecutionState();
    } catch (error) {
        console.error('❌ 停止工作流失败:', error);
        resetExecutionState();
    }
}

/**
 * 重置执行状态
 */
function resetExecutionState() {
    executionState.isRunning = false;
    executionState.isPaused = false;
    executionState.currentStep = 0;
    executionState.totalSteps = 0;

    updateExecutionControls();
    updateExecutionStatus('等待执行...', 0);
}

/**
 * 更新执行状态显示
 */
function updateExecutionStatus(message, progress) {
    const statusText = document.getElementById('execution-status')?.querySelector('.status-text');
    const progressFill = document.getElementById('progress-fill');
    const stepInfo = document.getElementById('step-info');

    if (statusText) {
        statusText.textContent = message;
    }

    if (progressFill && progress !== null) {
        progressFill.style.width = progress + '%';
    }

    if (stepInfo) {
        stepInfo.textContent = `步骤: ${executionState.currentStep}/${executionState.totalSteps}`;
    }
}

/**
 * 打开设计器
 */
function openDesigner() {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.sendMessage({
            action: 'openDesigner'
        }).catch(error => {
            console.error('打开设计器失败:', error);
            // 降级方案：直接打开URL
            const designerUrl = chrome.runtime.getURL('workflow-designer-mxgraph.html');
            window.open(designerUrl, '_blank');
        });
    }
}

/**
 * 发送消息到background script
 */
async function sendMessageToBackground(message) {
    return new Promise((resolve, reject) => {
        if (typeof chrome === 'undefined' || !chrome.runtime) {
            reject(new Error('Chrome runtime 不可用'));
            return;
        }

        chrome.runtime.sendMessage(message, (response) => {
            if (chrome.runtime.lastError) {
                reject(new Error(chrome.runtime.lastError.message));
            } else {
                resolve(response);
            }
        });
    });
}

/**
 * 处理来自background的消息
 */
function handleBackgroundMessage(request, sender, sendResponse) {
    console.log('🔔 浮层收到background消息:', request);

    switch (request.action) {
        case 'executionStarted':
            executionState.isRunning = true;
            executionState.isPaused = false;
            updateExecutionControls();
            updateExecutionStatus('正在执行...', null);
            break;

        case 'executionProgress':
            if (request.currentStep !== undefined) {
                executionState.currentStep = request.currentStep + 1;
                const progress = executionState.totalSteps > 0 ?
                    (executionState.currentStep / executionState.totalSteps) * 100 : 0;
                updateExecutionStatus(request.message || '正在执行...', progress);
            }
            break;

        case 'executionResult':
            if (request.result.completed) {
                resetExecutionState();
                if (request.result.success) {
                    updateExecutionStatus('执行完成', 100);
                } else {
                    updateExecutionStatus('执行失败: ' + (request.result.error || '未知错误'), null);
                }
            }
            break;

        case 'executionStopped':
            resetExecutionState();
            updateExecutionStatus('已停止', null);
            break;
    }

    sendResponse({ received: true });
}

// 导出到全局作用域供其他模块使用
window.FloatingPanel = {
    initialize: initializeFloatingPanel,
    show: () => {
        if (floatingPanel) {
            floatingPanel.style.display = 'block';
        }
    },
    hide: () => {
        if (floatingPanel) {
            floatingPanel.style.display = 'none';
        }
    },
    toggle: togglePanel,
    isVisible: () => floatingPanel && floatingPanel.style.display !== 'none'
};

console.log('✅ 浮层面板模块已加载');
