/**
 * 存储迁移工具
 * 将localStorage数据迁移到Chrome Extension Storage API
 */

import { 
    setStorageData, 
    getStorageData, 
    STORAGE_KEYS, 
    STORAGE_AREAS,
    WorkflowStorage 
} from './chrome-storage.js';

// localStorage键名映射到新的存储键名
const MIGRATION_MAP = {
    'automationWorkflows': STORAGE_KEYS.WORKFLOWS,
    'automation_state_cache': STORAGE_KEYS.STATE_CACHE,
    'automation_workflow_cache': STORAGE_KEYS.WORKFLOW_CACHE,
    'temp_edit_workflow': STORAGE_KEYS.TEMP_EDIT,
    'mxgraph_workflow': STORAGE_KEYS.MXGRAPH_WORKFLOW,
    'mxgraph_workflows': STORAGE_KEYS.MXGRAPH_WORKFLOWS,
    'designer_workflows': STORAGE_KEYS.DESIGNER_WORKFLOWS
};

/**
 * 检查是否需要迁移
 * @returns {Promise<boolean>} 是否需要迁移
 */
export async function needsMigration() {
    try {
        // 检查Chrome Storage中是否已有数据
        const existingWorkflows = await getStorageData(STORAGE_KEYS.WORKFLOWS);
        if (existingWorkflows && existingWorkflows.length > 0) {
            console.log('Chrome Storage 中已有数据，无需迁移');
            return false;
        }
        
        // 检查localStorage中是否有数据需要迁移
        if (typeof localStorage === 'undefined') {
            console.log('localStorage 不可用，无需迁移');
            return false;
        }
        
        for (const oldKey of Object.keys(MIGRATION_MAP)) {
            const data = localStorage.getItem(oldKey);
            if (data && data !== 'null' && data !== '[]') {
                console.log(`发现需要迁移的数据: ${oldKey}`);
                return true;
            }
        }
        
        console.log('localStorage 中没有需要迁移的数据');
        return false;
    } catch (error) {
        console.error('检查迁移需求时出错:', error);
        return false;
    }
}

/**
 * 执行数据迁移
 * @returns {Promise<object>} 迁移结果
 */
export async function migrateData() {
    const result = {
        success: false,
        migratedKeys: [],
        errors: [],
        totalItems: 0
    };
    
    try {
        console.log('🔄 开始数据迁移...');
        
        for (const [oldKey, newKey] of Object.entries(MIGRATION_MAP)) {
            try {
                const data = localStorage.getItem(oldKey);
                if (!data || data === 'null') {
                    continue;
                }
                
                // 尝试解析JSON数据
                let parsedData;
                try {
                    parsedData = JSON.parse(data);
                } catch (parseError) {
                    console.warn(`解析 ${oldKey} 数据失败:`, parseError);
                    // 如果不是JSON，直接存储字符串
                    parsedData = data;
                }
                
                // 保存到Chrome Storage
                const saved = await setStorageData(newKey, parsedData);
                if (saved) {
                    result.migratedKeys.push(oldKey);
                    
                    // 统计迁移的项目数量
                    if (Array.isArray(parsedData)) {
                        result.totalItems += parsedData.length;
                    } else {
                        result.totalItems += 1;
                    }
                    
                    console.log(`✅ 已迁移: ${oldKey} -> ${newKey}`);
                } else {
                    result.errors.push(`保存 ${newKey} 失败`);
                }
                
            } catch (error) {
                console.error(`迁移 ${oldKey} 时出错:`, error);
                result.errors.push(`${oldKey}: ${error.message}`);
            }
        }
        
        result.success = result.migratedKeys.length > 0;
        
        if (result.success) {
            console.log(`✅ 数据迁移完成! 迁移了 ${result.migratedKeys.length} 个键，共 ${result.totalItems} 个项目`);
        } else {
            console.log('ℹ️ 没有数据需要迁移');
        }
        
    } catch (error) {
        console.error('数据迁移过程中出错:', error);
        result.errors.push(`迁移过程错误: ${error.message}`);
    }
    
    return result;
}

/**
 * 清理已迁移的localStorage数据
 * @param {Array} migratedKeys - 已迁移的键名列表
 * @returns {Promise<boolean>} 是否成功
 */
export async function cleanupLocalStorage(migratedKeys) {
    try {
        if (typeof localStorage === 'undefined') {
            console.log('localStorage 不可用，无需清理');
            return true;
        }
        
        console.log('🧹 清理已迁移的localStorage数据...');
        
        for (const key of migratedKeys) {
            try {
                localStorage.removeItem(key);
                console.log(`🗑️ 已清理: ${key}`);
            } catch (error) {
                console.warn(`清理 ${key} 失败:`, error);
            }
        }
        
        console.log('✅ localStorage清理完成');
        return true;
    } catch (error) {
        console.error('清理localStorage时出错:', error);
        return false;
    }
}

/**
 * 完整的迁移流程
 * @param {boolean} autoCleanup - 是否自动清理localStorage
 * @returns {Promise<object>} 迁移结果
 */
export async function performMigration(autoCleanup = false) {
    try {
        // 检查是否需要迁移
        const needsToMigrate = await needsMigration();
        if (!needsToMigrate) {
            return {
                success: true,
                message: '无需迁移',
                migratedKeys: [],
                totalItems: 0
            };
        }
        
        // 执行迁移
        const migrationResult = await migrateData();
        
        // 自动清理（可选）
        if (autoCleanup && migrationResult.success && migrationResult.migratedKeys.length > 0) {
            await cleanupLocalStorage(migrationResult.migratedKeys);
        }
        
        return {
            ...migrationResult,
            message: migrationResult.success ? 
                `成功迁移 ${migrationResult.migratedKeys.length} 个数据项` : 
                '迁移失败'
        };
        
    } catch (error) {
        console.error('执行迁移流程时出错:', error);
        return {
            success: false,
            message: `迁移失败: ${error.message}`,
            migratedKeys: [],
            errors: [error.message],
            totalItems: 0
        };
    }
}

/**
 * 验证迁移结果
 * @returns {Promise<object>} 验证结果
 */
export async function validateMigration() {
    try {
        console.log('🔍 验证迁移结果...');
        
        const workflows = await WorkflowStorage.getWorkflows();
        const stateCache = await getStorageData(STORAGE_KEYS.STATE_CACHE);
        
        const validation = {
            workflowsCount: workflows ? workflows.length : 0,
            hasStateCache: !!stateCache,
            storageKeys: []
        };
        
        // 检查所有存储键
        for (const key of Object.values(STORAGE_KEYS)) {
            const data = await getStorageData(key);
            if (data !== null) {
                validation.storageKeys.push(key);
            }
        }
        
        console.log('✅ 迁移验证完成:', validation);
        return validation;
        
    } catch (error) {
        console.error('验证迁移结果时出错:', error);
        return {
            error: error.message,
            workflowsCount: 0,
            hasStateCache: false,
            storageKeys: []
        };
    }
}

console.log('✅ 存储迁移模块已加载');
