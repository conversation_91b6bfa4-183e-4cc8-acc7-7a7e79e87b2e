# 浮层面板功能使用指南

## 概述

浮层面板是一个植入到每个网页的可拖拽浮层，提供工作流执行控制功能。它使用Chrome Extension Storage API进行数据存储，支持跨标签页数据同步。

## 主要功能

### 1. 数据存储迁移
- ✅ 将所有localStorage使用迁移到Chrome Extension Storage API
- ✅ 提供自动数据迁移功能
- ✅ 支持降级到localStorage作为备选方案
- ✅ 跨标签页数据同步

### 2. 浮层面板
- ✅ 可拖拽的浮层界面
- ✅ 展开/折叠功能
- ✅ 工作流选择和执行控制
- ✅ 实时执行状态显示
- ✅ 进度条和步骤信息

### 3. 工作流管理
- ✅ 从Chrome Storage加载工作流列表
- ✅ 实时监听工作流数据变化
- ✅ 支持工作流执行、暂停、停止
- ✅ 集成设计器打开功能

## 文件结构

```
├── shared/storage/
│   ├── chrome-storage.js          # Chrome Storage API 统一管理
│   └── storage-migration.js       # 数据迁移工具
├── content/
│   ├── floating-panel.js          # 浮层面板主模块
│   └── content-modular.js         # 内容脚本模块加载器
├── modules/popup/
│   └── popup-storage.js           # 弹窗存储模块（已更新）
├── background/
│   └── background.js              # 后台脚本（已更新）
└── floating-panel-test.html       # 测试页面
```

## 使用方法

### 1. 安装和启用插件
1. 确保浏览器插件已正确安装
2. 在扩展管理页面启用插件
3. 访问任意网页，浮层会自动加载

### 2. 浮层操作
- **拖拽移动**：点击并拖拽浮层头部
- **展开/折叠**：点击右侧箭头按钮或双击头部
- **选择工作流**：从下拉列表中选择要执行的工作流
- **执行控制**：使用开始、暂停、停止按钮控制执行

### 3. 工作流管理
- **刷新列表**：点击🔄按钮刷新工作流列表
- **打开设计器**：点击底部链接打开工作流设计器
- **查看进度**：通过进度条和步骤信息查看执行状态

## 技术特性

### Chrome Storage API
```javascript
// 存储数据
await chrome.storage.local.set({ 'automationWorkflows': workflows });

// 读取数据
const result = await chrome.storage.local.get('automationWorkflows');

// 监听变化
chrome.storage.onChanged.addListener((changes, areaName) => {
    // 处理数据变化
});
```

### 数据迁移
```javascript
// 自动检查并迁移localStorage数据
const migrationResult = await performMigration();
if (migrationResult.success) {
    console.log('数据迁移成功');
}
```

### 浮层控制
```javascript
// 初始化浮层
await window.FloatingPanel.initialize();

// 显示/隐藏
window.FloatingPanel.show();
window.FloatingPanel.hide();

// 切换状态
window.FloatingPanel.toggle();
```

## 测试方法

### 1. 使用测试页面
1. 打开 `floating-panel-test.html`
2. 测试各项功能：
   - 存储读写测试
   - 浮层控制测试
   - 工作流管理测试
   - 消息通信测试

### 2. 手动测试
1. 安装插件后访问任意网页
2. 检查浮层是否正确显示
3. 测试拖拽、展开/折叠功能
4. 创建工作流并测试执行

### 3. 开发者工具调试
```javascript
// 检查浮层状态
console.log('FloatingPanel:', window.FloatingPanel);

// 检查存储数据
chrome.storage.local.get('automationWorkflows').then(console.log);

// 手动触发数据更新
window.dispatchEvent(new CustomEvent('workflowsUpdated'));
```

## 故障排除

### 常见问题

1. **浮层不显示**
   - 检查插件是否已启用
   - 查看控制台是否有错误信息
   - 确认页面通过http/https协议访问

2. **数据不同步**
   - 检查Chrome Storage权限
   - 验证数据迁移是否成功
   - 查看存储监听器是否正常工作

3. **工作流执行失败**
   - 检查background script连接
   - 验证工作流数据格式
   - 查看执行状态反馈

### 调试命令

```javascript
// 检查存储API可用性
console.log('Storage API:', typeof chrome !== 'undefined' && chrome.storage);

// 查看所有存储数据
chrome.storage.local.get(null).then(console.log);

// 清空所有数据
chrome.storage.local.clear();

// 手动迁移数据
import('./shared/storage/storage-migration.js').then(module => {
    module.performMigration().then(console.log);
});
```

## 配置选项

### 浮层样式
可以通过修改 `floating-panel.js` 中的CSS来自定义浮层外观：

```css
.automation-floating-panel {
    width: 280px;           /* 浮层宽度 */
    background: #ffffff;    /* 背景色 */
    border-radius: 8px;     /* 圆角 */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* 阴影 */
}
```

### 存储配置
可以在 `chrome-storage.js` 中修改存储配置：

```javascript
const STORAGE_KEYS = {
    WORKFLOWS: 'automationWorkflows',    // 主要工作流存储键
    STATE_CACHE: 'automation_state_cache', // 状态缓存键
    // ... 其他键名
};
```

## 更新日志

### v2.0.0
- ✅ 实现Chrome Extension Storage API迁移
- ✅ 添加浮层面板功能
- ✅ 支持跨标签页数据同步
- ✅ 提供自动数据迁移
- ✅ 增强错误处理和降级方案

### 下一步计划
- 🔄 添加浮层主题切换
- 🔄 支持浮层位置记忆
- 🔄 增加更多执行控制选项
- 🔄 优化移动端适配
