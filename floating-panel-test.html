<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浮层面板测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #007bff;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #1e7e34;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .status-display {
            background: #e9ecef;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 浮层面板测试页面</h1>
        
        <div class="info-box">
            <strong>说明：</strong>此页面用于测试浏览器插件的浮层面板功能。请确保已安装并启用了自动化助手插件。
        </div>
        
        <div class="test-section">
            <h2>📦 存储测试</h2>
            <p>测试Chrome Extension Storage API的功能</p>
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testStorageWrite()">写入测试数据</button>
                <button class="btn btn-secondary" onclick="testStorageRead()">读取数据</button>
                <button class="btn btn-danger" onclick="testStorageClear()">清空数据</button>
            </div>
            <div id="storage-status" class="status-display"></div>
        </div>
        
        <div class="test-section">
            <h2>🎛️ 浮层控制</h2>
            <p>测试浮层面板的显示和控制功能</p>
            <div class="test-buttons">
                <button class="btn btn-success" onclick="showFloatingPanel()">显示浮层</button>
                <button class="btn btn-secondary" onclick="hideFloatingPanel()">隐藏浮层</button>
                <button class="btn btn-primary" onclick="toggleFloatingPanel()">切换浮层</button>
                <button class="btn btn-danger" onclick="reinitializePanel()">重新初始化</button>
            </div>
            <div id="panel-status" class="status-display"></div>
        </div>
        
        <div class="test-section">
            <h2>🔄 工作流测试</h2>
            <p>测试工作流数据的加载和管理</p>
            <div class="form-group">
                <label for="workflow-name">工作流名称：</label>
                <input type="text" id="workflow-name" class="form-control" placeholder="输入工作流名称">
            </div>
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="createTestWorkflow()">创建测试工作流</button>
                <button class="btn btn-secondary" onclick="loadWorkflows()">加载工作流列表</button>
                <button class="btn btn-success" onclick="refreshFloatingPanel()">刷新浮层数据</button>
            </div>
            <div id="workflow-status" class="status-display"></div>
        </div>
        
        <div class="test-section">
            <h2>📱 消息通信测试</h2>
            <p>测试与background script的消息通信</p>
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testBackgroundMessage()">发送测试消息</button>
                <button class="btn btn-secondary" onclick="testExecutionStatus()">获取执行状态</button>
                <button class="btn btn-success" onclick="testOpenDesigner()">打开设计器</button>
            </div>
            <div id="message-status" class="status-display"></div>
        </div>
        
        <div class="warning-box">
            <strong>注意：</strong>如果某些功能无法正常工作，请检查：
            <ul>
                <li>浏览器插件是否已正确安装和启用</li>
                <li>页面是否通过http/https协议访问</li>
                <li>浏览器控制台是否有错误信息</li>
            </ul>
        </div>
    </div>

    <script>
        // 状态显示辅助函数
        function updateStatus(elementId, message) {
            const element = document.getElementById(elementId);
            if (element) {
                const timestamp = new Date().toLocaleTimeString();
                element.textContent += `[${timestamp}] ${message}\n`;
                element.scrollTop = element.scrollHeight;
            }
        }

        function clearStatus(elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = '';
            }
        }

        // 存储测试函数
        async function testStorageWrite() {
            clearStatus('storage-status');
            updateStatus('storage-status', '开始写入测试数据...');
            
            try {
                const testData = [
                    {
                        name: '测试工作流1',
                        steps: [
                            { action: 'click', locator: { strategy: 'id', value: 'test-button' } },
                            { action: 'input', locator: { strategy: 'id', value: 'test-input' }, text: 'Hello World' }
                        ],
                        createdAt: new Date().toISOString()
                    },
                    {
                        name: '测试工作流2',
                        steps: [
                            { action: 'click', locator: { strategy: 'css', value: '.test-class' } }
                        ],
                        createdAt: new Date().toISOString()
                    }
                ];
                
                if (typeof chrome !== 'undefined' && chrome.storage) {
                    await chrome.storage.local.set({ 'automationWorkflows': testData });
                    updateStatus('storage-status', '✅ 数据已写入Chrome Storage');
                } else {
                    localStorage.setItem('automationWorkflows', JSON.stringify(testData));
                    updateStatus('storage-status', '✅ 数据已写入localStorage (降级)');
                }
            } catch (error) {
                updateStatus('storage-status', '❌ 写入失败: ' + error.message);
            }
        }

        async function testStorageRead() {
            clearStatus('storage-status');
            updateStatus('storage-status', '开始读取数据...');
            
            try {
                let data;
                if (typeof chrome !== 'undefined' && chrome.storage) {
                    const result = await chrome.storage.local.get('automationWorkflows');
                    data = result.automationWorkflows;
                    updateStatus('storage-status', '📦 从Chrome Storage读取数据');
                } else {
                    const stored = localStorage.getItem('automationWorkflows');
                    data = stored ? JSON.parse(stored) : null;
                    updateStatus('storage-status', '📦 从localStorage读取数据');
                }
                
                if (data) {
                    updateStatus('storage-status', `✅ 读取成功，共 ${data.length} 个工作流`);
                    updateStatus('storage-status', JSON.stringify(data, null, 2));
                } else {
                    updateStatus('storage-status', 'ℹ️ 没有找到数据');
                }
            } catch (error) {
                updateStatus('storage-status', '❌ 读取失败: ' + error.message);
            }
        }

        async function testStorageClear() {
            clearStatus('storage-status');
            updateStatus('storage-status', '开始清空数据...');
            
            try {
                if (typeof chrome !== 'undefined' && chrome.storage) {
                    await chrome.storage.local.remove('automationWorkflows');
                    updateStatus('storage-status', '✅ Chrome Storage数据已清空');
                } else {
                    localStorage.removeItem('automationWorkflows');
                    updateStatus('storage-status', '✅ localStorage数据已清空');
                }
            } catch (error) {
                updateStatus('storage-status', '❌ 清空失败: ' + error.message);
            }
        }

        // 浮层控制函数
        function showFloatingPanel() {
            clearStatus('panel-status');
            updateStatus('panel-status', '尝试显示浮层面板...');
            
            if (window.FloatingPanel) {
                window.FloatingPanel.show();
                updateStatus('panel-status', '✅ 浮层面板已显示');
            } else {
                updateStatus('panel-status', '❌ 浮层面板模块未加载');
            }
        }

        function hideFloatingPanel() {
            clearStatus('panel-status');
            updateStatus('panel-status', '尝试隐藏浮层面板...');
            
            if (window.FloatingPanel) {
                window.FloatingPanel.hide();
                updateStatus('panel-status', '✅ 浮层面板已隐藏');
            } else {
                updateStatus('panel-status', '❌ 浮层面板模块未加载');
            }
        }

        function toggleFloatingPanel() {
            clearStatus('panel-status');
            updateStatus('panel-status', '切换浮层面板状态...');
            
            if (window.FloatingPanel) {
                window.FloatingPanel.toggle();
                updateStatus('panel-status', '✅ 浮层面板状态已切换');
            } else {
                updateStatus('panel-status', '❌ 浮层面板模块未加载');
            }
        }

        async function reinitializePanel() {
            clearStatus('panel-status');
            updateStatus('panel-status', '重新初始化浮层面板...');
            
            try {
                if (window.FloatingPanel && window.FloatingPanel.initialize) {
                    await window.FloatingPanel.initialize();
                    updateStatus('panel-status', '✅ 浮层面板重新初始化完成');
                } else {
                    updateStatus('panel-status', '❌ 浮层面板模块未加载或不支持重新初始化');
                }
            } catch (error) {
                updateStatus('panel-status', '❌ 重新初始化失败: ' + error.message);
            }
        }

        // 工作流测试函数
        async function createTestWorkflow() {
            clearStatus('workflow-status');
            const workflowName = document.getElementById('workflow-name').value || '测试工作流';
            updateStatus('workflow-status', `创建工作流: ${workflowName}`);
            
            try {
                const workflow = {
                    name: workflowName,
                    steps: [
                        { action: 'click', locator: { strategy: 'id', value: 'test-btn' } },
                        { action: 'input', locator: { strategy: 'css', value: 'input[type="text"]' }, text: '测试文本' },
                        { action: 'wait', duration: 1000 }
                    ],
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                
                // 获取现有工作流
                let workflows = [];
                if (typeof chrome !== 'undefined' && chrome.storage) {
                    const result = await chrome.storage.local.get('automationWorkflows');
                    workflows = result.automationWorkflows || [];
                } else {
                    const stored = localStorage.getItem('automationWorkflows');
                    workflows = stored ? JSON.parse(stored) : [];
                }
                
                // 添加新工作流
                workflows.push(workflow);
                
                // 保存
                if (typeof chrome !== 'undefined' && chrome.storage) {
                    await chrome.storage.local.set({ 'automationWorkflows': workflows });
                } else {
                    localStorage.setItem('automationWorkflows', JSON.stringify(workflows));
                }
                
                updateStatus('workflow-status', '✅ 工作流创建成功');
                updateStatus('workflow-status', JSON.stringify(workflow, null, 2));
            } catch (error) {
                updateStatus('workflow-status', '❌ 创建失败: ' + error.message);
            }
        }

        async function loadWorkflows() {
            clearStatus('workflow-status');
            updateStatus('workflow-status', '加载工作流列表...');
            
            try {
                let workflows;
                if (typeof chrome !== 'undefined' && chrome.storage) {
                    const result = await chrome.storage.local.get('automationWorkflows');
                    workflows = result.automationWorkflows || [];
                } else {
                    const stored = localStorage.getItem('automationWorkflows');
                    workflows = stored ? JSON.parse(stored) : [];
                }
                
                updateStatus('workflow-status', `✅ 加载完成，共 ${workflows.length} 个工作流`);
                workflows.forEach((workflow, index) => {
                    updateStatus('workflow-status', `${index + 1}. ${workflow.name} (${workflow.steps?.length || 0} 步骤)`);
                });
            } catch (error) {
                updateStatus('workflow-status', '❌ 加载失败: ' + error.message);
            }
        }

        function refreshFloatingPanel() {
            clearStatus('workflow-status');
            updateStatus('workflow-status', '刷新浮层面板数据...');
            
            // 触发存储变化事件
            window.dispatchEvent(new CustomEvent('workflowsUpdated', {
                detail: { source: 'manual-refresh' }
            }));
            
            updateStatus('workflow-status', '✅ 刷新事件已触发');
        }

        // 消息通信测试函数
        async function testBackgroundMessage() {
            clearStatus('message-status');
            updateStatus('message-status', '发送测试消息到background...');
            
            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    const response = await chrome.runtime.sendMessage({
                        action: 'test',
                        data: { timestamp: Date.now() }
                    });
                    updateStatus('message-status', '✅ 消息发送成功');
                    updateStatus('message-status', JSON.stringify(response, null, 2));
                } else {
                    updateStatus('message-status', '❌ Chrome runtime 不可用');
                }
            } catch (error) {
                updateStatus('message-status', '❌ 消息发送失败: ' + error.message);
            }
        }

        async function testExecutionStatus() {
            clearStatus('message-status');
            updateStatus('message-status', '获取执行状态...');
            
            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    const response = await chrome.runtime.sendMessage({
                        action: 'getExecutionStatus'
                    });
                    updateStatus('message-status', '✅ 状态获取成功');
                    updateStatus('message-status', JSON.stringify(response, null, 2));
                } else {
                    updateStatus('message-status', '❌ Chrome runtime 不可用');
                }
            } catch (error) {
                updateStatus('message-status', '❌ 状态获取失败: ' + error.message);
            }
        }

        async function testOpenDesigner() {
            clearStatus('message-status');
            updateStatus('message-status', '尝试打开设计器...');
            
            try {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    const response = await chrome.runtime.sendMessage({
                        action: 'openDesigner'
                    });
                    updateStatus('message-status', '✅ 设计器打开请求已发送');
                    updateStatus('message-status', JSON.stringify(response, null, 2));
                } else {
                    updateStatus('message-status', '❌ Chrome runtime 不可用');
                }
            } catch (error) {
                updateStatus('message-status', '❌ 打开设计器失败: ' + error.message);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('panel-status', '页面加载完成，检查浮层面板状态...');
            
            setTimeout(() => {
                if (window.FloatingPanel) {
                    updateStatus('panel-status', '✅ 浮层面板模块已加载');
                    if (window.FloatingPanel.isVisible && window.FloatingPanel.isVisible()) {
                        updateStatus('panel-status', '✅ 浮层面板当前可见');
                    } else {
                        updateStatus('panel-status', 'ℹ️ 浮层面板当前不可见');
                    }
                } else {
                    updateStatus('panel-status', '❌ 浮层面板模块未加载');
                    updateStatus('panel-status', '请检查插件是否正确安装和启用');
                }
            }, 1000);
        });
    </script>
</body>
</html>
