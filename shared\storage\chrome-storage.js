/**
 * Chrome Extension Storage API 统一管理模块
 * 替代localStorage，提供跨标签页同步和更好的安全性
 */

// 存储键名常量
export const STORAGE_KEYS = {
    WORKFLOWS: 'automationWorkflows',
    STATE_CACHE: 'automation_state_cache',
    WORKFLOW_CACHE: 'automation_workflow_cache',
    TEMP_EDIT: 'temp_edit_workflow',
    MXGRAPH_WORKFLOW: 'mxgraph_workflow',
    MXGRAPH_WORKFLOWS: 'mxgraph_workflows',
    DESIGNER_WORKFLOWS: 'designer_workflows'
};

// 存储区域类型
export const STORAGE_AREAS = {
    LOCAL: 'local',    // 本地存储，不同步
    SYNC: 'sync'       // 同步存储，跨设备同步
};

/**
 * 检查Chrome Extension Storage API是否可用
 * @returns {boolean} 是否可用
 */
export function isStorageApiAvailable() {
    return typeof chrome !== 'undefined' && 
           chrome.storage && 
           chrome.storage.local && 
           chrome.storage.sync;
}

/**
 * 获取存储区域对象
 * @param {string} area - 存储区域类型 ('local' 或 'sync')
 * @returns {object} Chrome storage area 对象
 */
function getStorageArea(area = STORAGE_AREAS.LOCAL) {
    if (!isStorageApiAvailable()) {
        throw new Error('Chrome Storage API 不可用');
    }
    
    return area === STORAGE_AREAS.SYNC ? chrome.storage.sync : chrome.storage.local;
}

/**
 * 存储数据
 * @param {string} key - 存储键名
 * @param {any} value - 要存储的值
 * @param {string} area - 存储区域 ('local' 或 'sync')
 * @returns {Promise<boolean>} 是否成功
 */
export async function setStorageData(key, value, area = STORAGE_AREAS.LOCAL) {
    try {
        const storage = getStorageArea(area);
        const data = { [key]: value };
        
        await storage.set(data);
        console.log(`✅ 数据已保存到 ${area} 存储:`, key);
        return true;
    } catch (error) {
        console.error(`❌ 保存数据失败 (${key}):`, error);
        return false;
    }
}

/**
 * 获取数据
 * @param {string|string[]} keys - 存储键名或键名数组
 * @param {string} area - 存储区域 ('local' 或 'sync')
 * @returns {Promise<any>} 存储的数据
 */
export async function getStorageData(keys, area = STORAGE_AREAS.LOCAL) {
    try {
        const storage = getStorageArea(area);
        const result = await storage.get(keys);
        
        // 如果是单个键，直接返回值
        if (typeof keys === 'string') {
            return result[keys] || null;
        }
        
        // 如果是多个键，返回整个结果对象
        return result;
    } catch (error) {
        console.error(`❌ 获取数据失败 (${keys}):`, error);
        return typeof keys === 'string' ? null : {};
    }
}

/**
 * 删除数据
 * @param {string|string[]} keys - 要删除的键名或键名数组
 * @param {string} area - 存储区域 ('local' 或 'sync')
 * @returns {Promise<boolean>} 是否成功
 */
export async function removeStorageData(keys, area = STORAGE_AREAS.LOCAL) {
    try {
        const storage = getStorageArea(area);
        await storage.remove(keys);
        console.log(`✅ 数据已删除:`, keys);
        return true;
    } catch (error) {
        console.error(`❌ 删除数据失败 (${keys}):`, error);
        return false;
    }
}

/**
 * 清空存储区域
 * @param {string} area - 存储区域 ('local' 或 'sync')
 * @returns {Promise<boolean>} 是否成功
 */
export async function clearStorage(area = STORAGE_AREAS.LOCAL) {
    try {
        const storage = getStorageArea(area);
        await storage.clear();
        console.log(`✅ ${area} 存储已清空`);
        return true;
    } catch (error) {
        console.error(`❌ 清空存储失败 (${area}):`, error);
        return false;
    }
}

/**
 * 获取存储使用情况
 * @param {string} area - 存储区域 ('local' 或 'sync')
 * @returns {Promise<object>} 使用情况信息
 */
export async function getStorageUsage(area = STORAGE_AREAS.LOCAL) {
    try {
        const storage = getStorageArea(area);
        
        if (storage.getBytesInUse) {
            const bytesInUse = await storage.getBytesInUse();
            const quota = area === STORAGE_AREAS.SYNC ? 102400 : 10485760; // sync: 100KB, local: 10MB
            
            return {
                bytesInUse,
                quota,
                available: quota - bytesInUse,
                usagePercentage: ((bytesInUse / quota) * 100).toFixed(2)
            };
        } else {
            return {
                bytesInUse: null,
                quota: null,
                available: null,
                usagePercentage: null,
                note: '浏览器不支持存储使用量查询'
            };
        }
    } catch (error) {
        console.error(`❌ 获取存储使用情况失败 (${area}):`, error);
        return null;
    }
}

/**
 * 监听存储变化
 * @param {function} callback - 变化回调函数
 * @returns {function} 取消监听的函数
 */
export function onStorageChanged(callback) {
    if (!isStorageApiAvailable()) {
        console.warn('Chrome Storage API 不可用，无法监听存储变化');
        return () => {};
    }
    
    const listener = (changes, areaName) => {
        console.log(`📦 存储变化 (${areaName}):`, changes);
        callback(changes, areaName);
    };
    
    chrome.storage.onChanged.addListener(listener);
    
    // 返回取消监听的函数
    return () => {
        chrome.storage.onChanged.removeListener(listener);
    };
}

/**
 * 工作流数据专用方法
 */
export const WorkflowStorage = {
    /**
     * 保存工作流列表
     * @param {Array} workflows - 工作流列表
     * @returns {Promise<boolean>} 是否成功
     */
    async saveWorkflows(workflows) {
        if (!Array.isArray(workflows)) {
            console.error('工作流数据必须是数组');
            return false;
        }
        
        return await setStorageData(STORAGE_KEYS.WORKFLOWS, workflows);
    },
    
    /**
     * 获取工作流列表
     * @returns {Promise<Array>} 工作流列表
     */
    async getWorkflows() {
        const workflows = await getStorageData(STORAGE_KEYS.WORKFLOWS);
        return workflows || [];
    },
    
    /**
     * 添加单个工作流
     * @param {object} workflow - 工作流对象
     * @returns {Promise<boolean>} 是否成功
     */
    async addWorkflow(workflow) {
        const workflows = await this.getWorkflows();
        
        // 检查是否已存在同名工作流
        const existingIndex = workflows.findIndex(w => w.name === workflow.name);
        if (existingIndex >= 0) {
            workflows[existingIndex] = workflow;
        } else {
            workflows.push(workflow);
        }
        
        return await this.saveWorkflows(workflows);
    },
    
    /**
     * 删除工作流
     * @param {string} workflowName - 工作流名称
     * @returns {Promise<boolean>} 是否成功
     */
    async removeWorkflow(workflowName) {
        const workflows = await this.getWorkflows();
        const filteredWorkflows = workflows.filter(w => w.name !== workflowName);
        
        return await this.saveWorkflows(filteredWorkflows);
    }
};

/**
 * 状态缓存专用方法
 */
export const StateCache = {
    /**
     * 保存执行状态
     * @param {object} state - 状态对象
     * @returns {Promise<boolean>} 是否成功
     */
    async saveState(state) {
        const stateData = {
            ...state,
            timestamp: Date.now(),
            url: typeof window !== 'undefined' ? window.location.href : null
        };
        
        return await setStorageData(STORAGE_KEYS.STATE_CACHE, stateData);
    },
    
    /**
     * 获取执行状态
     * @returns {Promise<object|null>} 状态对象
     */
    async getState() {
        return await getStorageData(STORAGE_KEYS.STATE_CACHE);
    },
    
    /**
     * 清除状态缓存
     * @returns {Promise<boolean>} 是否成功
     */
    async clearState() {
        return await removeStorageData(STORAGE_KEYS.STATE_CACHE);
    }
};

console.log('✅ Chrome Storage 模块已加载');
